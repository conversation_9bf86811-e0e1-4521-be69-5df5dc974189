* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #2d3748;
    padding: 20px;
}

.container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 24px;
    padding: 32px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(255, 255, 255, 0.5);
    max-width: 640px;
    width: 100%;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

h1 {
    text-align: center;
    margin-bottom: 32px;
    color: #1a202c;
    font-size: 2.5em;
    font-weight: 700;
    letter-spacing: -0.025em;
}

h2 {
    text-align: center;
    margin-bottom: 24px;
    color: #2d3748;
    font-size: 1.5em;
    font-weight: 600;
}

/* 游戏模式选择 */
.game-mode-selection {
    text-align: center;
    padding: 32px 0;
}

.mode-buttons {
    display: flex;
    gap: 24px;
    justify-content: center;
    flex-wrap: wrap;
}

.mode-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 24px 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 16px;
    font-size: 1.1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    min-width: 140px;
}

.mode-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.mode-btn:active {
    transform: translateY(0);
}

.mode-icon {
    font-size: 2em;
}

/* 游戏界面 */
.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding: 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 20px;
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.player-info {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.current-player {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    font-size: 1.1em;
    color: #2d3748;
}

.player-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.6);
}

.score {
    display: flex;
    gap: 24px;
}

.score-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 700;
    font-size: 1.3em;
    color: #1a202c;
}

.piece {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid rgba(45, 55, 72, 0.2);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), inset 0 1px 2px rgba(255, 255, 255, 0.3);
    position: relative;
}

.piece::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 30%;
    width: 30%;
    height: 30%;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    filter: blur(2px);
}

.piece.black {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
}

.piece.white {
    background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%);
}

.game-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.control-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 12px;
    font-size: 0.95em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 120px;
}

.restart-btn {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(245, 101, 101, 0.3);
}

.restart-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(245, 101, 101, 0.4);
}

.change-mode-btn {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
}

.change-mode-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(66, 153, 225, 0.4);
}

.game-board {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: repeat(8, 1fr);
    gap: 3px;
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    padding: 16px;
    border-radius: 20px;
    margin-bottom: 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), inset 0 1px 2px rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.cell {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.cell::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    pointer-events: none;
}

.cell:hover {
    background: linear-gradient(135deg, #68d391 0%, #48bb78 100%);
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

.cell.valid-move {
    background: linear-gradient(135deg, #f6ad55 0%, #ed8936 100%);
    box-shadow: 0 0 20px rgba(246, 173, 85, 0.6), inset 0 1px 2px rgba(255, 255, 255, 0.2);
    animation: pulse 2s infinite;
}

.cell.valid-move:hover {
    background: linear-gradient(135deg, #fbb454 0%, #f6ad55 100%);
    transform: scale(1.05);
}

.cell .piece {
    width: 44px;
    height: 44px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.cell .piece.flipping {
    animation: flip 0.8s ease-in-out;
}

@keyframes flip {
    0% { transform: scaleX(1) rotateY(0deg); }
    50% { transform: scaleX(0) rotateY(90deg); }
    100% { transform: scaleX(1) rotateY(0deg); }
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 0 20px rgba(246, 173, 85, 0.6), inset 0 1px 2px rgba(255, 255, 255, 0.2);
    }
    50% {
        box-shadow: 0 0 30px rgba(246, 173, 85, 0.8), inset 0 1px 2px rgba(255, 255, 255, 0.3);
    }
}

/* AI思考指示器 */
.ai-thinking {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 16px;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
    border-radius: 16px;
    border: 1px solid rgba(129, 230, 217, 0.3);
    color: #234e52;
    font-weight: 600;
}

.thinking-animation {
    display: flex;
    gap: 4px;
}

.thinking-animation .dot {
    width: 8px;
    height: 8px;
    background: #38b2ac;
    border-radius: 50%;
    animation: thinking 1.4s infinite ease-in-out;
}

.thinking-animation .dot:nth-child(1) { animation-delay: -0.32s; }
.thinking-animation .dot:nth-child(2) { animation-delay: -0.16s; }
.thinking-animation .dot:nth-child(3) { animation-delay: 0s; }

@keyframes thinking {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

.game-status {
    text-align: center;
    font-size: 1.2em;
    font-weight: 600;
    margin-bottom: 24px;
    padding: 16px;
    border-radius: 16px;
    background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
    color: #22543d;
    border: 1px solid rgba(72, 187, 120, 0.2);
}

.game-status.warning {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    color: #92400e;
    border: 1px solid rgba(251, 191, 36, 0.2);
}

.game-status.game-over {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    color: #991b1b;
    border: 1px solid rgba(248, 113, 113, 0.2);
}

.rules {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 24px;
    border-radius: 20px;
    margin-top: 24px;
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.rules h3 {
    margin-bottom: 16px;
    color: #1a202c;
    font-weight: 600;
}

.rules ul {
    list-style: none;
    line-height: 1.7;
}

.rules li {
    margin-bottom: 10px;
    color: #4a5568;
    position: relative;
    padding-left: 24px;
}

.rules li::before {
    content: '•';
    color: #667eea;
    font-weight: bold;
    position: absolute;
    left: 0;
    font-size: 1.2em;
}

@media (max-width: 768px) {
    body {
        padding: 12px;
    }

    .container {
        padding: 24px;
        margin: 0;
    }

    h1 {
        font-size: 2em;
        margin-bottom: 24px;
    }

    .game-info {
        flex-direction: column;
        gap: 16px;
        padding: 20px;
    }

    .mode-buttons {
        flex-direction: column;
        align-items: center;
    }

    .mode-btn {
        width: 100%;
        max-width: 200px;
    }

    .game-controls {
        width: 100%;
    }

    .control-btn {
        width: 100%;
    }

    .cell {
        width: 42px;
        height: 42px;
    }

    .cell .piece {
        width: 34px;
        height: 34px;
    }

    .piece {
        width: 28px;
        height: 28px;
    }
}

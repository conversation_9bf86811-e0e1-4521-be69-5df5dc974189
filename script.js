class OthelloGame {
    constructor() {
        this.board = Array(8).fill().map(() => Array(8).fill(0));
        this.currentPlayer = 1; // 1 = 黑棋, -1 = 白棋
        this.gameOver = false;
        this.gameMode = null; // 'pvp' = 人人对战, 'pve' = 人机对战
        this.aiPlayer = -1; // AI默认为白棋
        this.aiDifficulty = 'normal'; // 'beginner', 'normal', 'master'
        this.isAiThinking = false;
        this.eventsbound = false; // 防止重复绑定事件
        this.directions = [
            [-1, -1], [-1, 0], [-1, 1],
            [0, -1],           [0, 1],
            [1, -1],  [1, 0],  [1, 1]
        ];

        // 位置权重表（用于AI评估）
        this.positionWeights = [
            [100, -20, 10, 5, 5, 10, -20, 100],
            [-20, -50, -2, -2, -2, -2, -50, -20],
            [10, -2, -1, -1, -1, -1, -2, 10],
            [5, -2, -1, -1, -1, -1, -2, 5],
            [5, -2, -1, -1, -1, -1, -2, 5],
            [10, -2, -1, -1, -1, -1, -2, 10],
            [-20, -50, -2, -2, -2, -2, -50, -20],
            [100, -20, 10, 5, 5, 10, -20, 100]
        ];

        this.bindEvents();
        this.showModeSelection();
    }
    
    showModeSelection() {
        document.getElementById('gameModeSelection').style.display = 'block';
        document.getElementById('aiDifficultySelection').style.display = 'none';
        document.getElementById('gameInterface').style.display = 'none';
    }

    showDifficultySelection() {
        document.getElementById('gameModeSelection').style.display = 'none';
        document.getElementById('aiDifficultySelection').style.display = 'block';
        document.getElementById('gameInterface').style.display = 'none';
    }

    startGame(mode, difficulty = 'normal') {
        this.gameMode = mode;
        this.aiDifficulty = difficulty;
        this.board = Array(8).fill().map(() => Array(8).fill(0));
        this.currentPlayer = 1;
        this.gameOver = false;
        this.isAiThinking = false;

        document.getElementById('gameModeSelection').style.display = 'none';
        document.getElementById('aiDifficultySelection').style.display = 'none';
        document.getElementById('gameInterface').style.display = 'block';

        this.initializeBoard();
        this.createBoardUI();
        this.updateUI();
    }

    initializeBoard() {
        // 初始化中央4个棋子
        this.board[3][3] = -1; // 白棋
        this.board[3][4] = 1;  // 黑棋
        this.board[4][3] = 1;  // 黑棋
        this.board[4][4] = -1; // 白棋
    }
    
    createBoardUI() {
        const gameBoard = document.getElementById('gameBoard');
        gameBoard.innerHTML = '';
        
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                cell.dataset.row = row;
                cell.dataset.col = col;
                
                cell.addEventListener('click', () => this.handleCellClick(row, col));
                gameBoard.appendChild(cell);
            }
        }
    }
    
    updateUI() {
        const cells = document.querySelectorAll('.cell');
        cells.forEach(cell => {
            const row = parseInt(cell.dataset.row);
            const col = parseInt(cell.dataset.col);
            const value = this.board[row][col];

            cell.innerHTML = '';
            cell.classList.remove('valid-move');

            if (value !== 0) {
                const piece = document.createElement('div');
                piece.className = `piece ${value === 1 ? 'black' : 'white'}`;
                cell.appendChild(piece);
            }
        });

        // 检查游戏状态
        if (!this.gameOver) {
            // 检查当前玩家是否能下棋
            if (!this.hasValidMoves(this.currentPlayer)) {
                // 当前玩家无法下棋，检查游戏是否应该结束
                if (!this.hasValidMoves(-this.currentPlayer)) {
                    // 双方都无法下棋，游戏结束
                    this.gameOver = true;
                }
            }

            // 显示可下棋的位置
            if (!this.gameOver) {
                this.showValidMoves();
            }
        }

        this.updateScore();
        this.updateCurrentPlayer();
        this.updateGameStatus();
    }
    
    showValidMoves() {
        // 只在人类玩家回合或人人对战模式下显示可下棋位置
        if (this.gameMode === 'pvp' || (this.gameMode === 'pve' && this.currentPlayer !== this.aiPlayer)) {
            for (let row = 0; row < 8; row++) {
                for (let col = 0; col < 8; col++) {
                    if (this.isValidMove(row, col, this.currentPlayer)) {
                        const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                        cell.classList.add('valid-move');
                    }
                }
            }
        }
    }
    
    isValidMove(row, col, player) {
        if (this.board[row][col] !== 0) return false;
        
        for (let [dx, dy] of this.directions) {
            if (this.checkDirection(row, col, dx, dy, player)) {
                return true;
            }
        }
        return false;
    }
    
    checkDirection(row, col, dx, dy, player) {
        let x = row + dx;
        let y = col + dy;
        let hasOpponent = false;
        
        while (x >= 0 && x < 8 && y >= 0 && y < 8) {
            if (this.board[x][y] === 0) return false;
            if (this.board[x][y] === -player) {
                hasOpponent = true;
            } else if (this.board[x][y] === player) {
                return hasOpponent;
            }
            x += dx;
            y += dy;
        }
        return false;
    }
    
    makeMove(row, col) {
        if (!this.isValidMove(row, col, this.currentPlayer)) return false;
        
        this.board[row][col] = this.currentPlayer;
        
        // 翻转棋子
        for (let [dx, dy] of this.directions) {
            this.flipDirection(row, col, dx, dy, this.currentPlayer);
        }
        
        return true;
    }
    
    flipDirection(row, col, dx, dy, player) {
        if (!this.checkDirection(row, col, dx, dy, player)) return;
        
        let x = row + dx;
        let y = col + dy;
        const toFlip = [];
        
        while (x >= 0 && x < 8 && y >= 0 && y < 8) {
            if (this.board[x][y] === 0) break;
            if (this.board[x][y] === -player) {
                toFlip.push([x, y]);
            } else if (this.board[x][y] === player) {
                // 翻转所有中间的棋子
                toFlip.forEach(([fx, fy]) => {
                    this.board[fx][fy] = player;
                });
                break;
            }
            x += dx;
            y += dy;
        }
    }
    
    hasValidMoves(player) {
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (this.isValidMove(row, col, player)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    handleCellClick(row, col) {
        if (this.gameOver || this.isAiThinking) return;

        // 在人机对战模式下，只允许人类玩家点击
        if (this.gameMode === 'pve' && this.currentPlayer === this.aiPlayer) return;

        // 检查当前玩家是否有可下的棋
        if (!this.hasValidMoves(this.currentPlayer)) {
            // 当前玩家无法下棋，检查游戏是否结束
            if (this.checkGameEnd()) {
                this.updateUI();
                return;
            } else {
                // 跳过当前玩家回合
                this.switchToNextPlayer();
                this.updateUI();

                // 如果切换后轮到AI，让AI下棋
                if (this.gameMode === 'pve' && this.currentPlayer === this.aiPlayer && !this.gameOver) {
                    this.aiMove();
                }
                return;
            }
        }

        if (this.makeMove(row, col)) {
            this.processMove();
        }
    }

    processMove() {
        this.updateUI();

        // 检查游戏是否结束
        if (this.checkGameEnd()) {
            return;
        }

        // 切换到下一个玩家
        this.switchToNextPlayer();

        this.updateUI();

        // 在人机对战模式下，如果轮到AI，让AI下棋
        if (this.gameMode === 'pve' && this.currentPlayer === this.aiPlayer && !this.gameOver) {
            this.aiMove();
        }
    }

    checkGameEnd() {
        const currentPlayerCanMove = this.hasValidMoves(this.currentPlayer);
        const nextPlayerCanMove = this.hasValidMoves(-this.currentPlayer);

        if (!currentPlayerCanMove && !nextPlayerCanMove) {
            // 双方都无法下棋，游戏结束
            this.gameOver = true;
            return true;
        }

        return false;
    }

    switchToNextPlayer() {
        const nextPlayer = -this.currentPlayer;

        if (this.hasValidMoves(nextPlayer)) {
            // 下一个玩家可以下棋，正常切换
            this.currentPlayer = nextPlayer;
        } else if (this.hasValidMoves(this.currentPlayer)) {
            // 下一个玩家无法下棋，但当前玩家还能下棋，跳过下一个玩家的回合
            // 当前玩家保持不变
        } else {
            // 双方都无法下棋，游戏应该已经在checkGameEnd中结束了
            this.gameOver = true;
        }
    }
    
    updateScore() {
        let blackCount = 0;
        let whiteCount = 0;
        
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (this.board[row][col] === 1) blackCount++;
                else if (this.board[row][col] === -1) whiteCount++;
            }
        }
        
        document.getElementById('blackScore').textContent = blackCount;
        document.getElementById('whiteScore').textContent = whiteCount;
    }
    
    // AI相关方法
    async aiMove() {
        if (this.gameOver || this.isAiThinking) return;

        this.isAiThinking = true;
        this.showAiThinking(true);

        // 根据难度设置不同的思考时间
        const thinkingTime = this.getAiThinkingTime();
        await new Promise(resolve => setTimeout(resolve, thinkingTime));

        const bestMove = this.getBestMove();
        if (bestMove) {
            this.makeMove(bestMove.row, bestMove.col);
            this.processMove();
        } else {
            // AI无法下棋，检查游戏是否结束
            if (this.checkGameEnd()) {
                this.updateUI();
            } else {
                // 跳过AI回合，切换到人类玩家
                this.switchToNextPlayer();
                this.updateUI();
            }
        }

        this.isAiThinking = false;
        this.showAiThinking(false);
    }

    getAiThinkingTime() {
        const baseTime = {
            'beginner': 500,
            'normal': 1000,
            'master': 2000
        };

        const randomVariation = Math.random() * 500;
        return baseTime[this.aiDifficulty] + randomVariation;
    }

    getBestMove() {
        const validMoves = this.getValidMoves(this.aiPlayer);
        if (validMoves.length === 0) return null;

        switch (this.aiDifficulty) {
            case 'beginner':
                return this.getBeginnerMove(validMoves);
            case 'normal':
                return this.getNormalMove(validMoves);
            case 'master':
                return this.getMasterMove(validMoves);
            default:
                return this.getNormalMove(validMoves);
        }
    }

    getValidMoves(player) {
        const moves = [];
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (this.isValidMove(row, col, player)) {
                    moves.push({ row, col });
                }
            }
        }
        return moves;
    }

    // 新手级别：简单贪心算法
    getBeginnerMove(validMoves) {
        let bestMove = null;
        let bestScore = -1;

        for (const move of validMoves) {
            // 简单评估：主要看翻转的棋子数量
            const flippedCount = this.countFlippedPieces(move.row, move.col, this.aiPlayer);

            // 添加一些随机性，让新手级别不那么机械
            const randomBonus = Math.random() * 5;
            const score = flippedCount + randomBonus;

            if (score > bestScore) {
                bestScore = score;
                bestMove = move;
            }
        }

        return bestMove;
    }

    // 普通级别：使用Minimax算法
    getNormalMove(validMoves) {
        let bestMove = null;
        let bestScore = -Infinity;

        for (const move of validMoves) {
            const boardCopy = this.copyBoard();
            this.simulateMove(move.row, move.col, this.aiPlayer);

            const score = this.minimax(2, false, -Infinity, Infinity);

            this.board = boardCopy;

            if (score > bestScore) {
                bestScore = score;
                bestMove = move;
            }
        }

        return bestMove;
    }

    // 大师级别：使用更深层的Alpha-Beta剪枝
    getMasterMove(validMoves) {
        let bestMove = null;
        let bestScore = -Infinity;

        for (const move of validMoves) {
            const boardCopy = this.copyBoard();
            this.simulateMove(move.row, move.col, this.aiPlayer);

            const score = this.minimax(5, false, -Infinity, Infinity);

            this.board = boardCopy;

            if (score > bestScore) {
                bestScore = score;
                bestMove = move;
            }
        }

        return bestMove;
    }

    // Minimax算法实现
    minimax(depth, isMaximizing, alpha, beta) {
        if (depth === 0 || this.isGameOver()) {
            return this.evaluateBoard();
        }

        const currentPlayer = isMaximizing ? this.aiPlayer : -this.aiPlayer;
        const validMoves = this.getValidMoves(currentPlayer);

        if (validMoves.length === 0) {
            // 如果当前玩家无法移动，切换到对手
            return this.minimax(depth - 1, !isMaximizing, alpha, beta);
        }

        if (isMaximizing) {
            let maxScore = -Infinity;
            for (const move of validMoves) {
                const boardCopy = this.copyBoard();
                this.simulateMove(move.row, move.col, currentPlayer);

                const score = this.minimax(depth - 1, false, alpha, beta);
                this.board = boardCopy;

                maxScore = Math.max(maxScore, score);
                alpha = Math.max(alpha, score);

                if (beta <= alpha) break; // Alpha-Beta剪枝
            }
            return maxScore;
        } else {
            let minScore = Infinity;
            for (const move of validMoves) {
                const boardCopy = this.copyBoard();
                this.simulateMove(move.row, move.col, currentPlayer);

                const score = this.minimax(depth - 1, true, alpha, beta);
                this.board = boardCopy;

                minScore = Math.min(minScore, score);
                beta = Math.min(beta, score);

                if (beta <= alpha) break; // Alpha-Beta剪枝
            }
            return minScore;
        }
    }

    // 增强的棋盘评估函数
    evaluateBoard() {
        let score = 0;
        const totalPieces = this.getTotalPieces();
        const gamePhase = this.getGamePhase(totalPieces);

        // 1. 棋子数量差异
        const pieceCount = this.getPieceCount();
        score += (pieceCount.ai - pieceCount.opponent) * this.getPieceWeight(gamePhase);

        // 2. 位置权重
        score += this.getPositionalScore();

        // 3. 移动性（可选移动数）
        const aiMoves = this.getValidMoves(this.aiPlayer).length;
        const opponentMoves = this.getValidMoves(-this.aiPlayer).length;
        score += (aiMoves - opponentMoves) * this.getMobilityWeight(gamePhase);

        // 4. 稳定性评估
        score += this.getStabilityScore();

        // 5. 边缘控制
        score += this.getEdgeScore();

        return score;
    }

    copyBoard() {
        return this.board.map(row => [...row]);
    }

    simulateMove(row, col, player) {
        this.board[row][col] = player;
        for (let [dx, dy] of this.directions) {
            this.flipDirection(row, col, dx, dy, player);
        }
    }

    isGameOver() {
        return !this.hasValidMoves(this.aiPlayer) && !this.hasValidMoves(-this.aiPlayer);
    }

    getTotalPieces() {
        let count = 0;
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (this.board[row][col] !== 0) count++;
            }
        }
        return count;
    }

    getGamePhase(totalPieces) {
        if (totalPieces < 20) return 'opening';
        if (totalPieces < 50) return 'middle';
        return 'endgame';
    }

    getPieceCount() {
        let ai = 0, opponent = 0;
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (this.board[row][col] === this.aiPlayer) ai++;
                else if (this.board[row][col] === -this.aiPlayer) opponent++;
            }
        }
        return { ai, opponent };
    }

    getPieceWeight(gamePhase) {
        switch (gamePhase) {
            case 'opening': return 1;
            case 'middle': return 2;
            case 'endgame': return 10;
            default: return 2;
        }
    }

    getPositionalScore() {
        let score = 0;
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (this.board[row][col] === this.aiPlayer) {
                    score += this.positionWeights[row][col];
                } else if (this.board[row][col] === -this.aiPlayer) {
                    score -= this.positionWeights[row][col];
                }
            }
        }
        return score;
    }

    getMobilityWeight(gamePhase) {
        switch (gamePhase) {
            case 'opening': return 10;
            case 'middle': return 5;
            case 'endgame': return 2;
            default: return 5;
        }
    }

    getStabilityScore() {
        let score = 0;

        // 检查角落稳定性
        const corners = [[0,0], [0,7], [7,0], [7,7]];
        for (const [row, col] of corners) {
            if (this.board[row][col] === this.aiPlayer) score += 100;
            else if (this.board[row][col] === -this.aiPlayer) score -= 100;
        }

        return score;
    }

    getEdgeScore() {
        let score = 0;

        // 检查边缘控制
        for (let i = 0; i < 8; i++) {
            // 上下边缘
            if (this.board[0][i] === this.aiPlayer) score += 10;
            else if (this.board[0][i] === -this.aiPlayer) score -= 10;

            if (this.board[7][i] === this.aiPlayer) score += 10;
            else if (this.board[7][i] === -this.aiPlayer) score -= 10;

            // 左右边缘
            if (this.board[i][0] === this.aiPlayer) score += 10;
            else if (this.board[i][0] === -this.aiPlayer) score -= 10;

            if (this.board[i][7] === this.aiPlayer) score += 10;
            else if (this.board[i][7] === -this.aiPlayer) score -= 10;
        }

        return score;
    }

    countFlippedPieces(row, col, player) {
        let count = 0;
        for (let [dx, dy] of this.directions) {
            count += this.countFlippedInDirection(row, col, dx, dy, player);
        }
        return count;
    }

    countFlippedInDirection(row, col, dx, dy, player) {
        if (!this.checkDirection(row, col, dx, dy, player)) return 0;

        let x = row + dx;
        let y = col + dy;
        let count = 0;

        while (x >= 0 && x < 8 && y >= 0 && y < 8) {
            if (this.board[x][y] === 0) break;
            if (this.board[x][y] === -player) {
                count++;
            } else if (this.board[x][y] === player) {
                return count;
            }
            x += dx;
            y += dy;
        }
        return 0;
    }

    countValidMoves(player) {
        let count = 0;
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (this.isValidMove(row, col, player)) {
                    count++;
                }
            }
        }
        return count;
    }

    showAiThinking(show) {
        const aiThinking = document.getElementById('aiThinking');
        if (show) {
            const difficultyNames = {
                'beginner': '新手',
                'normal': '普通',
                'master': '大师'
            };
            const difficultyText = difficultyNames[this.aiDifficulty] || '普通';
            const thinkingText = aiThinking.querySelector('span');
            thinkingText.textContent = `AI正在思考... (${difficultyText}级别)`;
        }
        aiThinking.style.display = show ? 'flex' : 'none';
    }

    updateCurrentPlayer() {
        const indicator = document.getElementById('currentPlayer');
        const piece = indicator.querySelector('.piece');
        const text = indicator.querySelector('span');

        if (this.currentPlayer === 1) {
            piece.className = 'piece black';
            if (this.gameMode === 'pve' && this.currentPlayer === this.aiPlayer) {
                text.textContent = 'AI (黑棋)';
            } else {
                text.textContent = '黑棋';
            }
        } else {
            piece.className = 'piece white';
            if (this.gameMode === 'pve' && this.currentPlayer === this.aiPlayer) {
                text.textContent = 'AI (白棋)';
            } else {
                text.textContent = '白棋';
            }
        }
    }
    
    updateGameStatus() {
        const status = document.getElementById('gameStatus');

        if (this.gameOver) {
            const blackScore = parseInt(document.getElementById('blackScore').textContent);
            const whiteScore = parseInt(document.getElementById('whiteScore').textContent);

            let message;
            if (this.gameMode === 'pve') {
                // 人机对战模式的结果显示
                const humanWins = (this.aiPlayer === 1 && whiteScore > blackScore) ||
                                 (this.aiPlayer === -1 && blackScore > whiteScore);
                const aiWins = (this.aiPlayer === 1 && blackScore > whiteScore) ||
                              (this.aiPlayer === -1 && whiteScore > blackScore);

                if (humanWins) {
                    message = `恭喜！你获胜了！(${blackScore} : ${whiteScore})`;
                } else if (aiWins) {
                    message = `AI获胜！再试一次吧！(${blackScore} : ${whiteScore})`;
                } else {
                    message = `平局！势均力敌！(${blackScore} : ${whiteScore})`;
                }
            } else {
                // 人人对战模式的结果显示
                if (blackScore > whiteScore) {
                    message = `游戏结束！黑棋获胜！(${blackScore} : ${whiteScore})`;
                } else if (whiteScore > blackScore) {
                    message = `游戏结束！白棋获胜！(${whiteScore} : ${blackScore})`;
                } else {
                    message = `游戏结束！平局！(${blackScore} : ${whiteScore})`;
                }
            }

            status.textContent = message;
            status.className = 'game-status game-over';
        } else if (!this.hasValidMoves(this.currentPlayer)) {
            const playerName = this.gameMode === 'pve' && this.currentPlayer === this.aiPlayer ? 'AI' :
                              (this.currentPlayer === 1 ? '黑棋' : '白棋');
            status.textContent = `${playerName}无法下棋，跳过回合`;
            status.className = 'game-status warning';
        } else {
            status.textContent = '';
            status.className = 'game-status';
        }
    }
    
    restart() {
        if (this.gameMode) {
            this.startGame(this.gameMode);
        } else {
            this.showModeSelection();
        }
    }

    bindEvents() {
        // 绑定游戏内按钮事件（重新开始和切换模式）
        document.addEventListener('click', (e) => {
            // 重新开始按钮
            if (e.target.id === 'restartBtn') {
                e.preventDefault();
                this.restart();
                return;
            }

            // 切换模式按钮
            if (e.target.id === 'changeModeBtn') {
                e.preventDefault();
                this.showModeSelection();
                return;
            }
        });
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    const game = new OthelloGame();

    // 绑定模式选择按钮事件
    document.querySelectorAll('.mode-btn[data-mode]').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const mode = btn.dataset.mode;
            console.log('Mode selected:', mode);

            if (mode === 'pve') {
                // 如果选择人机对战，显示难度选择界面
                game.showDifficultySelection();
            } else {
                // 如果选择人人对战，直接开始游戏
                game.startGame(mode);
            }
        });
    });

    // 绑定难度选择按钮事件
    document.querySelectorAll('.difficulty-btn[data-difficulty]').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const difficulty = btn.dataset.difficulty;
            console.log('Difficulty selected:', difficulty);
            game.startGame('pve', difficulty);
        });
    });

    // 绑定返回按钮事件
    document.getElementById('backToModeBtn').addEventListener('click', (e) => {
        e.preventDefault();
        game.showModeSelection();
    });

    console.log('Game initialized and events bound');
});

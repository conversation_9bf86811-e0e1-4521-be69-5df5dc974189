class OthelloGame {
    constructor() {
        this.board = Array(8).fill().map(() => Array(8).fill(0));
        this.currentPlayer = 1; // 1 = 黑棋, -1 = 白棋
        this.gameOver = false;
        this.gameMode = null; // 'pvp' = 人人对战, 'pve' = 人机对战
        this.aiPlayer = -1; // AI默认为白棋
        this.isAiThinking = false;
        this.eventsbound = false; // 防止重复绑定事件
        this.directions = [
            [-1, -1], [-1, 0], [-1, 1],
            [0, -1],           [0, 1],
            [1, -1],  [1, 0],  [1, 1]
        ];

        // 位置权重表（用于AI评估）
        this.positionWeights = [
            [100, -20, 10, 5, 5, 10, -20, 100],
            [-20, -50, -2, -2, -2, -2, -50, -20],
            [10, -2, -1, -1, -1, -1, -2, 10],
            [5, -2, -1, -1, -1, -1, -2, 5],
            [5, -2, -1, -1, -1, -1, -2, 5],
            [10, -2, -1, -1, -1, -1, -2, 10],
            [-20, -50, -2, -2, -2, -2, -50, -20],
            [100, -20, 10, 5, 5, 10, -20, 100]
        ];

        this.bindEvents();
        this.showModeSelection();
    }
    
    showModeSelection() {
        document.getElementById('gameModeSelection').style.display = 'block';
        document.getElementById('gameInterface').style.display = 'none';
    }

    startGame(mode) {
        this.gameMode = mode;
        this.board = Array(8).fill().map(() => Array(8).fill(0));
        this.currentPlayer = 1;
        this.gameOver = false;
        this.isAiThinking = false;

        document.getElementById('gameModeSelection').style.display = 'none';
        document.getElementById('gameInterface').style.display = 'block';

        this.initializeBoard();
        this.createBoardUI();
        this.updateUI();
    }

    initializeBoard() {
        // 初始化中央4个棋子
        this.board[3][3] = -1; // 白棋
        this.board[3][4] = 1;  // 黑棋
        this.board[4][3] = 1;  // 黑棋
        this.board[4][4] = -1; // 白棋
    }
    
    createBoardUI() {
        const gameBoard = document.getElementById('gameBoard');
        gameBoard.innerHTML = '';
        
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                cell.dataset.row = row;
                cell.dataset.col = col;
                
                cell.addEventListener('click', () => this.handleCellClick(row, col));
                gameBoard.appendChild(cell);
            }
        }
    }
    
    updateUI() {
        const cells = document.querySelectorAll('.cell');
        cells.forEach(cell => {
            const row = parseInt(cell.dataset.row);
            const col = parseInt(cell.dataset.col);
            const value = this.board[row][col];
            
            cell.innerHTML = '';
            cell.classList.remove('valid-move');
            
            if (value !== 0) {
                const piece = document.createElement('div');
                piece.className = `piece ${value === 1 ? 'black' : 'white'}`;
                cell.appendChild(piece);
            }
        });
        
        // 显示可下棋的位置
        if (!this.gameOver) {
            this.showValidMoves();
        }
        
        this.updateScore();
        this.updateCurrentPlayer();
        this.updateGameStatus();
    }
    
    showValidMoves() {
        // 只在人类玩家回合或人人对战模式下显示可下棋位置
        if (this.gameMode === 'pvp' || (this.gameMode === 'pve' && this.currentPlayer !== this.aiPlayer)) {
            for (let row = 0; row < 8; row++) {
                for (let col = 0; col < 8; col++) {
                    if (this.isValidMove(row, col, this.currentPlayer)) {
                        const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                        cell.classList.add('valid-move');
                    }
                }
            }
        }
    }
    
    isValidMove(row, col, player) {
        if (this.board[row][col] !== 0) return false;
        
        for (let [dx, dy] of this.directions) {
            if (this.checkDirection(row, col, dx, dy, player)) {
                return true;
            }
        }
        return false;
    }
    
    checkDirection(row, col, dx, dy, player) {
        let x = row + dx;
        let y = col + dy;
        let hasOpponent = false;
        
        while (x >= 0 && x < 8 && y >= 0 && y < 8) {
            if (this.board[x][y] === 0) return false;
            if (this.board[x][y] === -player) {
                hasOpponent = true;
            } else if (this.board[x][y] === player) {
                return hasOpponent;
            }
            x += dx;
            y += dy;
        }
        return false;
    }
    
    makeMove(row, col) {
        if (!this.isValidMove(row, col, this.currentPlayer)) return false;
        
        this.board[row][col] = this.currentPlayer;
        
        // 翻转棋子
        for (let [dx, dy] of this.directions) {
            this.flipDirection(row, col, dx, dy, this.currentPlayer);
        }
        
        return true;
    }
    
    flipDirection(row, col, dx, dy, player) {
        if (!this.checkDirection(row, col, dx, dy, player)) return;
        
        let x = row + dx;
        let y = col + dy;
        const toFlip = [];
        
        while (x >= 0 && x < 8 && y >= 0 && y < 8) {
            if (this.board[x][y] === 0) break;
            if (this.board[x][y] === -player) {
                toFlip.push([x, y]);
            } else if (this.board[x][y] === player) {
                // 翻转所有中间的棋子
                toFlip.forEach(([fx, fy]) => {
                    this.board[fx][fy] = player;
                });
                break;
            }
            x += dx;
            y += dy;
        }
    }
    
    hasValidMoves(player) {
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (this.isValidMove(row, col, player)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    handleCellClick(row, col) {
        if (this.gameOver || this.isAiThinking) return;

        // 在人机对战模式下，只允许人类玩家点击
        if (this.gameMode === 'pve' && this.currentPlayer === this.aiPlayer) return;

        if (this.makeMove(row, col)) {
            this.processMove();
        }
    }

    processMove() {
        this.updateUI();

        // 检查下一个玩家是否有可下的棋
        const nextPlayer = -this.currentPlayer;
        if (this.hasValidMoves(nextPlayer)) {
            this.currentPlayer = nextPlayer;
        } else if (!this.hasValidMoves(this.currentPlayer)) {
            // 双方都无法下棋，游戏结束
            this.gameOver = true;
        }
        // 如果只有当前玩家无法下棋，则跳过回合，继续当前玩家

        this.updateUI();

        // 在人机对战模式下，如果轮到AI，让AI下棋
        if (this.gameMode === 'pve' && this.currentPlayer === this.aiPlayer && !this.gameOver) {
            this.aiMove();
        }
    }
    
    updateScore() {
        let blackCount = 0;
        let whiteCount = 0;
        
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (this.board[row][col] === 1) blackCount++;
                else if (this.board[row][col] === -1) whiteCount++;
            }
        }
        
        document.getElementById('blackScore').textContent = blackCount;
        document.getElementById('whiteScore').textContent = whiteCount;
    }
    
    // AI相关方法
    async aiMove() {
        if (this.gameOver || this.isAiThinking) return;

        this.isAiThinking = true;
        this.showAiThinking(true);

        // 模拟AI思考时间
        await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 400));

        const bestMove = this.getBestMove();
        if (bestMove) {
            this.makeMove(bestMove.row, bestMove.col);
            this.processMove();
        }

        this.isAiThinking = false;
        this.showAiThinking(false);
    }

    getBestMove() {
        const validMoves = [];

        // 获取所有可能的移动
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (this.isValidMove(row, col, this.aiPlayer)) {
                    validMoves.push({ row, col });
                }
            }
        }

        if (validMoves.length === 0) return null;

        // 评估每个移动
        let bestMove = null;
        let bestScore = -Infinity;

        for (const move of validMoves) {
            const score = this.evaluateMove(move.row, move.col);
            if (score > bestScore) {
                bestScore = score;
                bestMove = move;
            }
        }

        return bestMove;
    }

    evaluateMove(row, col) {
        // 创建棋盘副本进行模拟
        const boardCopy = this.board.map(row => [...row]);
        const originalBoard = this.board;

        // 模拟移动
        this.board = boardCopy;
        this.makeMove(row, col);

        let score = 0;

        // 1. 位置权重
        score += this.positionWeights[row][col];

        // 2. 翻转的棋子数量
        const flippedCount = this.countFlippedPieces(row, col, this.aiPlayer);
        score += flippedCount * 10;

        // 3. 稳定性（角落和边缘）
        if ((row === 0 || row === 7) && (col === 0 || col === 7)) {
            score += 500; // 角落非常重要
        } else if (row === 0 || row === 7 || col === 0 || col === 7) {
            score += 50; // 边缘也很重要
        }

        // 4. 移动性（对手的可选移动数）
        const opponentMoves = this.countValidMoves(-this.aiPlayer);
        score -= opponentMoves * 5;

        // 恢复原始棋盘
        this.board = originalBoard;

        return score;
    }

    countFlippedPieces(row, col, player) {
        let count = 0;
        for (let [dx, dy] of this.directions) {
            count += this.countFlippedInDirection(row, col, dx, dy, player);
        }
        return count;
    }

    countFlippedInDirection(row, col, dx, dy, player) {
        if (!this.checkDirection(row, col, dx, dy, player)) return 0;

        let x = row + dx;
        let y = col + dy;
        let count = 0;

        while (x >= 0 && x < 8 && y >= 0 && y < 8) {
            if (this.board[x][y] === 0) break;
            if (this.board[x][y] === -player) {
                count++;
            } else if (this.board[x][y] === player) {
                return count;
            }
            x += dx;
            y += dy;
        }
        return 0;
    }

    countValidMoves(player) {
        let count = 0;
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (this.isValidMove(row, col, player)) {
                    count++;
                }
            }
        }
        return count;
    }

    showAiThinking(show) {
        const aiThinking = document.getElementById('aiThinking');
        aiThinking.style.display = show ? 'flex' : 'none';
    }

    updateCurrentPlayer() {
        const indicator = document.getElementById('currentPlayer');
        const piece = indicator.querySelector('.piece');
        const text = indicator.querySelector('span');

        if (this.currentPlayer === 1) {
            piece.className = 'piece black';
            if (this.gameMode === 'pve' && this.currentPlayer === this.aiPlayer) {
                text.textContent = 'AI (黑棋)';
            } else {
                text.textContent = '黑棋';
            }
        } else {
            piece.className = 'piece white';
            if (this.gameMode === 'pve' && this.currentPlayer === this.aiPlayer) {
                text.textContent = 'AI (白棋)';
            } else {
                text.textContent = '白棋';
            }
        }
    }
    
    updateGameStatus() {
        const status = document.getElementById('gameStatus');

        if (this.gameOver) {
            const blackScore = parseInt(document.getElementById('blackScore').textContent);
            const whiteScore = parseInt(document.getElementById('whiteScore').textContent);

            let message;
            if (this.gameMode === 'pve') {
                // 人机对战模式的结果显示
                const humanWins = (this.aiPlayer === 1 && whiteScore > blackScore) ||
                                 (this.aiPlayer === -1 && blackScore > whiteScore);
                const aiWins = (this.aiPlayer === 1 && blackScore > whiteScore) ||
                              (this.aiPlayer === -1 && whiteScore > blackScore);

                if (humanWins) {
                    message = `恭喜！你获胜了！(${blackScore} : ${whiteScore})`;
                } else if (aiWins) {
                    message = `AI获胜！再试一次吧！(${blackScore} : ${whiteScore})`;
                } else {
                    message = `平局！势均力敌！(${blackScore} : ${whiteScore})`;
                }
            } else {
                // 人人对战模式的结果显示
                if (blackScore > whiteScore) {
                    message = `游戏结束！黑棋获胜！(${blackScore} : ${whiteScore})`;
                } else if (whiteScore > blackScore) {
                    message = `游戏结束！白棋获胜！(${whiteScore} : ${blackScore})`;
                } else {
                    message = `游戏结束！平局！(${blackScore} : ${whiteScore})`;
                }
            }

            status.textContent = message;
            status.className = 'game-status game-over';
        } else if (!this.hasValidMoves(this.currentPlayer)) {
            const playerName = this.gameMode === 'pve' && this.currentPlayer === this.aiPlayer ? 'AI' :
                              (this.currentPlayer === 1 ? '黑棋' : '白棋');
            status.textContent = `${playerName}无法下棋，跳过回合`;
            status.className = 'game-status warning';
        } else {
            status.textContent = '';
            status.className = 'game-status';
        }
    }
    
    restart() {
        if (this.gameMode) {
            this.startGame(this.gameMode);
        } else {
            this.showModeSelection();
        }
    }

    bindEvents() {
        // 绑定游戏内按钮事件（重新开始和切换模式）
        document.addEventListener('click', (e) => {
            // 重新开始按钮
            if (e.target.id === 'restartBtn') {
                e.preventDefault();
                this.restart();
                return;
            }

            // 切换模式按钮
            if (e.target.id === 'changeModeBtn') {
                e.preventDefault();
                this.showModeSelection();
                return;
            }
        });
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    const game = new OthelloGame();

    // 直接绑定模式选择按钮事件
    document.querySelectorAll('.mode-btn[data-mode]').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const mode = btn.dataset.mode;
            console.log('Mode selected:', mode);
            game.startGame(mode);
        });
    });

    console.log('Game initialized and events bound');
});
